import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/app_theme.dart';

class ContactScreen extends StatelessWidget {
  const ContactScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Contact Us')),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryColor,
                    AppTheme.primaryColor.withOpacity(0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Column(
                children: [
                  Icon(Icons.contact_support, size: 64, color: Colors.white),
                  const SizedBox(height: 16),
                  Text(
                    'Get in Touch',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'We\'re here to help! Reach out to us through any of the channels below.',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyLarge?.copyWith(color: Colors.white70),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // Contact Methods
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Contact Information',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Email Support
                  _buildContactCard(
                    context,
                    'Email Support',
                    '<EMAIL>',
                    'Send us an email for general inquiries and support',
                    Icons.email,
                    AppTheme.primaryColor,
                    () => _copyToClipboard(
                      context,
                      '<EMAIL>',
                      'Email',
                    ),
                  ),

                  // Phone Support
                  _buildContactCard(
                    context,
                    'Phone Support',
                    '+255625290997',
                    'Call us for urgent issues and immediate assistance',
                    Icons.phone,
                    AppTheme.successColor,
                    () => _copyToClipboard(
                      context,
                      '+255625290997',
                      'Phone number',
                    ),
                  ),

                  // Technical Support
                  _buildContactCard(
                    context,
                    'Technical Support',
                    '<EMAIL>',
                    'Report bugs, technical issues, and feature requests',
                    Icons.build,
                    AppTheme.warningColor,
                    () => _copyToClipboard(
                      context,
                      '<EMAIL>',
                      'Technical support email',
                    ),
                  ),

                  // Business Hours
                  _buildContactCard(
                    context,
                    'Business Hours',
                    'Mon - Fri: 9:00 AM - 6:00 PM EST',
                    'We typically respond within 24 hours during business days',
                    Icons.schedule,
                    AppTheme.infoColor,
                    null,
                  ),

                  const SizedBox(height: 24),

                  // Office Address
                  Text(
                    'Office Address',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.location_on,
                                color: AppTheme.errorColor,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Reporta Headquarters',
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            '001 Kananura Street\nKivule 1102\nDar es salaam, Tanzania',
                            style: Theme.of(
                              context,
                            ).textTheme.bodyLarge?.copyWith(height: 1.5),
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            width: double.infinity,
                            child: OutlinedButton.icon(
                              onPressed:
                                  () => _copyToClipboard(
                                    context,
                                    '001 Kananura Street, Kivule 1102, Dar es salaam, Tanzania',
                                    'Address',
                                  ),
                              icon: const Icon(Icons.copy),
                              label: const Text('Copy Address'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Social Media
                  Text(
                    'Follow Us',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Expanded(
                        child: _buildSocialCard(
                          context,
                          'Twitter',
                          '@Kadilana1',
                          Icons.alternate_email,
                          const Color(0xFF1DA1F2),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildSocialCard(
                          context,
                          'LinkedIn',
                          'Kadilana Mbogo',
                          Icons.business,
                          const Color(0xFF0077B5),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Emergency Contact
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppTheme.errorColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppTheme.errorColor.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.emergency,
                              color: AppTheme.errorColor,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Emergency Support',
                              style: Theme.of(
                                context,
                              ).textTheme.titleMedium?.copyWith(
                                color: AppTheme.errorColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          'For critical issues affecting your business operations:',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '📞 Emergency Hotline: +255625290997\n'
                          '📧 Emergency Email: <EMAIL>\n'
                          '⏰ Available 24/7 for critical issues',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(height: 1.5),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Response Time Info
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppTheme.infoColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppTheme.infoColor.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info,
                              color: AppTheme.infoColor,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Response Times',
                              style: Theme.of(
                                context,
                              ).textTheme.titleMedium?.copyWith(
                                color: AppTheme.infoColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          '• General Inquiries: Within 24 hours\n'
                          '• Technical Issues: Within 12 hours\n'
                          '• Bug Reports: Within 6 hours\n'
                          '• Emergency Issues: Within 1 hour\n'
                          '• Feature Requests: Within 48 hours',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(height: 1.6),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactCard(
    BuildContext context,
    String title,
    String contact,
    String description,
    IconData icon,
    Color color,
    VoidCallback? onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: color.withOpacity(0.1),
                child: Icon(icon, color: color),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      contact,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              if (onTap != null)
                Icon(Icons.copy, color: AppTheme.textSecondaryColor, size: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSocialCard(
    BuildContext context,
    String platform,
    String handle,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: InkWell(
        onTap: () => _copyToClipboard(context, handle, platform),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                platform,
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                handle,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: color),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _copyToClipboard(BuildContext context, String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$label copied to clipboard'),
        backgroundColor: AppTheme.successColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
