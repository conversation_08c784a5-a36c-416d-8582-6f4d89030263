import 'app_localizations.dart';

class AppLocalizationsSw extends AppLocalizations {
  // Common
  @override
  String get appName => 'Reporta';
  @override
  String get ok => '<PERSON>a';
  @override
  String get cancel => '<PERSON><PERSON><PERSON>';
  @override
  String get save => 'Hi<PERSON>dhi';
  @override
  String get delete => 'Futa';
  @override
  String get edit => 'Hariri';
  @override
  String get add => 'Ongeza';
  @override
  String get search => 'Tafuta';
  @override
  String get loading => 'Inapakia...';
  @override
  String get error => 'Hitilafu';
  @override
  String get success => 'Mafanikio';
  @override
  String get warning => 'Onyo';
  @override
  String get info => 'Taarifa';
  
  // Authentication
  @override
  String get login => 'Ingia';
  @override
  String get logout => 'Toka';
  @override
  String get email => 'Barua pepe';
  @override
  String get password => 'Nenosiri';
  @override
  String get name => 'Jina';
  @override
  String get signUp => 'Jisajili';
  @override
  String get forgotPassword => 'Umesahau nenosiri?';
  @override
  String get resetPassword => 'Weka nenosiri jipya';
  @override
  String get confirmPassword => 'Thibitisha nenosiri';
  @override
  String get securityQuestion => 'Swali la usalama';
  @override
  String get securityAnswer => 'Jibu la usalama';
  
  // Navigation
  @override
  String get dashboard => 'Dashibodi';
  @override
  String get products => 'Bidhaa';
  @override
  String get reports => 'Ripoti';
  @override
  String get settings => 'Mipangilio';
  @override
  String get account => 'Akaunti';
  @override
  String get help => 'Msaada';
  @override
  String get contact => 'Mawasiliano';
  @override
  String get feedback => 'Maoni';
  @override
  String get complaints => 'Malalamiko';
  @override
  String get privacyPolicy => 'Sera ya Faragha';
  
  // Products
  @override
  String get productName => 'Jina la bidhaa';
  @override
  String get description => 'Maelezo';
  @override
  String get price => 'Bei';
  @override
  String get category => 'Jamii';
  @override
  String get currency => 'Sarafu';
  @override
  String get stockQuantity => 'Idadi ya hisa';
  @override
  String get lowStockThreshold => 'Kiwango cha hisa chini';
  @override
  String get addProduct => 'Ongeza bidhaa';
  @override
  String get editProduct => 'Hariri bidhaa';
  @override
  String get deleteProduct => 'Futa bidhaa';
  @override
  String get stockIn => 'Hisa ndani';
  @override
  String get stockOut => 'Hisa nje';
  @override
  String get recordSale => 'Rekodi mauzo';
  
  // Dashboard
  @override
  String get totalProducts => 'Jumla ya bidhaa';
  @override
  String get lowStockAlerts => 'Tahadhari za hisa chini';
  @override
  String get totalSales => 'Jumla ya mauzo';
  @override
  String get quickActions => 'Vitendo vya haraka';
  @override
  String get recentActivity => 'Shughuli za hivi karibuni';
  @override
  String get stockHistory => 'Historia ya hisa';
  
  // Reports
  @override
  String get overview => 'Muhtasari';
  @override
  String get stockReport => 'Ripoti ya hisa';
  @override
  String get transactionHistory => 'Historia ya miamala';
  @override
  String get salesReport => 'Ripoti ya mauzo';
  @override
  String get lowStockReport => 'Ripoti ya hisa chini';
  
  // Settings
  @override
  String get theme => 'Mandhari';
  @override
  String get language => 'Lugha';
  @override
  String get notifications => 'Arifa';
  @override
  String get lightTheme => 'Mwanga';
  @override
  String get darkTheme => 'Giza';
  @override
  String get systemTheme => 'Mfumo';
  
  // Messages
  @override
  String get welcomeMessage => 'Karibu Reporta!';
  @override
  String get loginSuccess => 'Umeingia kwa mafanikio';
  @override
  String get loginError => 'Barua pepe au nenosiri si sahihi';
  @override
  String get productAdded => 'Bidhaa imeongezwa kwa mafanikio';
  @override
  String get productUpdated => 'Bidhaa imesasishwa kwa mafanikio';
  @override
  String get productDeleted => 'Bidhaa imefutwa kwa mafanikio';
  @override
  String get stockUpdated => 'Hisa imesasishwa kwa mafanikio';
  @override
  String get saleRecorded => 'Mauzo yamerekodiwa kwa mafanikio';
  
  // Validation
  @override
  String get fieldRequired => 'Sehemu hii inahitajika';
  @override
  String get invalidEmail => 'Tafadhali ingiza barua pepe sahihi';
  @override
  String get passwordTooShort => 'Nenosiri lazima liwe na angalau herufi 6';
  @override
  String get passwordsDoNotMatch => 'Nenosiri hazilingani';
  @override
  String get invalidPrice => 'Tafadhali ingiza bei sahihi';
  @override
  String get invalidQuantity => 'Tafadhali ingiza idadi sahihi';
}
